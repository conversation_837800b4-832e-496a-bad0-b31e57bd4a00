# ZaiYu Dating App - Admin Panel

A comprehensive PHP-based admin panel for managing the ZaiYu dating platform with Bootstrap frontend.

## Features

### 🔐 Authentication
- Secure admin login system
- Session management with timeout
- Password change functionality

### 👥 User Management
- View all users with pagination
- Search and filter users by name, phone, gender, status
- Block/unblock users
- Edit user information
- Delete users (soft delete)
- User statistics and demographics

### 💕 Match Management
- View all matches between users
- See match statistics
- Message count per match
- Delete matches if needed

### 📅 Activity Management
- Manage platform activities/events
- View participant counts
- Activity statistics
- Create, edit, and delete activities

### 📊 Reports & Analytics
- User registration trends
- Match creation statistics
- Age distribution charts
- Location-based analytics
- Activity participation reports
- Exportable reports

### ⚙️ Settings
- Change admin password
- Configure application settings
- System information display
- Database status monitoring

## Installation

### Prerequisites
- Apache web server
- PHP 7.4 or higher
- PostgreSQL database
- PDO PostgreSQL extension

### Setup Instructions

1. **Copy Admin Panel Files**
   ```bash
   # Copy the admin folder to your web server directory
   cp -r WooTrade/zaiyuapp/admin /var/www/html/zaiyuapp-admin
   # or for XAMPP/WAMP
   cp -r WooTrade/zaiyuapp/admin C:/xampp/htdocs/zaiyuapp-admin
   ```

2. **Configure Database Connection**
   Edit `config/database.php` and update the database credentials:
   ```php
   private $host = 'localhost';
   private $port = '5432';
   private $db_name = 'secondchance_db';
   private $username = 'secondchance';
   private $password = 'password';
   ```

3. **Set Permissions**
   ```bash
   # Make sure Apache can read the files
   chmod -R 755 /var/www/html/zaiyuapp-admin
   chown -R www-data:www-data /var/www/html/zaiyuapp-admin
   ```

4. **Configure Apache Virtual Host (Optional)**
   Create a virtual host for the admin panel:
   ```apache
   <VirtualHost *:80>
       ServerName admin.zaiyuapp.local
       DocumentRoot /var/www/html/zaiyuapp-admin
       
       <Directory /var/www/html/zaiyuapp-admin>
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

5. **Access the Admin Panel**
   - URL: `http://localhost/zaiyuapp-admin` or `http://admin.zaiyuapp.local`
   - Default credentials:
     - Username: `admin`
     - Password: `admin123`

## Security Configuration

### 🔒 Change Default Credentials
**IMPORTANT**: Change the default admin credentials in production!

Edit `config/database.php`:
```php
const ADMIN_USERNAME = 'your_admin_username';
const ADMIN_PASSWORD = 'your_secure_password';
```

### 🛡️ Production Security Checklist
- [ ] Change default admin credentials
- [ ] Use HTTPS in production
- [ ] Store passwords as hashes in database
- [ ] Implement rate limiting for login attempts
- [ ] Add IP whitelisting if needed
- [ ] Enable PHP error logging (disable display_errors)
- [ ] Set secure session configuration

## File Structure

```
admin/
├── config/
│   └── database.php          # Database configuration and utilities
├── includes/
│   ├── auth.php             # Authentication system
│   ├── header.php           # Common header with navigation
│   └── footer.php           # Common footer with scripts
├── login.php                # Admin login page
├── logout.php               # Logout handler
├── index.php                # Main entry point (redirects)
├── dashboard.php            # Dashboard with statistics
├── users.php                # User management
├── matches.php              # Match management
├── activities.php           # Activity management
├── reports.php              # Reports and analytics
├── settings.php             # Admin settings
└── README.md               # This file
```

## Database Requirements

The admin panel expects the following PostgreSQL tables:
- `users` - User accounts
- `matches` - User matches
- `messages` - Chat messages
- `activities` - Platform activities
- `activity_participants` - Activity participation
- `photos` - User photos

## Customization

### 🎨 Styling
The admin panel uses Bootstrap 5 with custom CSS. To customize:
1. Edit the `<style>` section in `includes/header.php`
2. Modify CSS variables in `:root` selector
3. Add custom CSS classes as needed

### 🔧 Configuration
Edit `config/database.php` to modify:
- Items per page: `AdminConfig::ITEMS_PER_PAGE`
- Session timeout: `AdminConfig::SESSION_TIMEOUT`
- App name and version: `AdminConfig::APP_NAME`, `AdminConfig::APP_VERSION`

### 📊 Adding New Features
1. Create new PHP file in root directory
2. Include authentication: `require_once 'includes/auth.php';`
3. Set page title: `$page_title = 'Your Page';`
4. Include header and footer
5. Add navigation link in `includes/header.php`

## Troubleshooting

### Database Connection Issues
1. Check PostgreSQL is running
2. Verify database credentials in `config/database.php`
3. Ensure PDO PostgreSQL extension is installed
4. Check database user permissions

### Permission Errors
```bash
# Fix file permissions
chmod -R 755 /path/to/admin
chown -R www-data:www-data /path/to/admin
```

### Session Issues
1. Check PHP session configuration
2. Verify session directory is writable
3. Check session timeout settings

## API Integration

The admin panel reads directly from the PostgreSQL database used by your Go backend. No additional API setup is required.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify database connection and permissions
3. Check Apache error logs
4. Ensure all PHP extensions are installed

## License

This admin panel is part of the ZaiYu Dating App project.
