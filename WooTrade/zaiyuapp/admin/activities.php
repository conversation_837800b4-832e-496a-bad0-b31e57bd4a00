<?php
/**
 * Activities Management Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'Activities';
$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// Handle activity actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $activity_id = (int)($_POST['activity_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'delete':
                $stmt = $db->prepare("DELETE FROM activities WHERE id = ?");
                $stmt->execute([$activity_id]);
                $success_message = "Activity deleted successfully.";
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = AdminConfig::ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

try {
    // Get total count
    $stmt = $db->query("SELECT COUNT(*) as total FROM activities");
    $total_activities = $stmt->fetch()['total'];
    $total_pages = ceil($total_activities / $limit);
    
    // Get activities with participant count
    $query = "
        SELECT a.*, 
               (SELECT COUNT(*) FROM activity_participants ap WHERE ap.activity_id = a.id AND ap.status = 'joined') as participant_count,
               u.name as creator_name
        FROM activities a
        LEFT JOIN users u ON a.creator_id = u.id
        ORDER BY a.start_time DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $db->query($query);
    $activities = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error fetching activities: " . $e->getMessage();
    $activities = [];
    $total_activities = 0;
    $total_pages = 0;
}

include 'includes/header.php';
?>

<!-- Activities Content -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt"></i> Activities Management
            </h1>
            <button class="btn btn-primary" onclick="addActivity()">
                <i class="fas fa-plus"></i> Add Activity
            </button>
        </div>
    </div>
</div>

<?php if ($success_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($success_message, 'success'); ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Activities Grid -->
<div class="row">
    <?php if (!empty($activities)): ?>
        <?php foreach ($activities as $activity): ?>
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-truncate"><?php echo htmlspecialchars($activity['title']); ?></h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editActivity(<?php echo $activity['id']; ?>)">
                                    <i class="fas fa-edit"></i> Edit
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="viewParticipants(<?php echo $activity['id']; ?>)">
                                    <i class="fas fa-users"></i> View Participants
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="activity_id" value="<?php echo $activity['id']; ?>">
                                        <button type="submit" class="dropdown-item text-danger btn-delete">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text text-muted small mb-2">
                            <?php echo htmlspecialchars(substr($activity['description'], 0, 100)); ?>
                            <?php if (strlen($activity['description']) > 100): ?>...<?php endif; ?>
                        </p>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-tag"></i> <?php echo htmlspecialchars($activity['category']); ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> <?php echo formatDate($activity['start_time']); ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($activity['location']); ?>
                            </small>
                        </div>
                        
                        <?php if ($activity['creator_name']): ?>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-user"></i> Created by <?php echo htmlspecialchars($activity['creator_name']); ?>
                            </small>
                        </div>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary">
                                <?php echo $activity['participant_count']; ?> / <?php echo $activity['max_participants']; ?> participants
                            </span>
                            
                            <?php
                            $now = new DateTime();
                            $start_time = new DateTime($activity['start_time']);
                            if ($start_time > $now): ?>
                                <span class="badge bg-success">Upcoming</span>
                            <?php elseif ($start_time->format('Y-m-d') === $now->format('Y-m-d')): ?>
                                <span class="badge bg-warning">Today</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Past</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-calendar-alt fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">No activities found</h4>
                <p class="text-muted">Create your first activity to get started.</p>
                <button class="btn btn-primary" onclick="addActivity()">
                    <i class="fas fa-plus"></i> Add Activity
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Activities pagination">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Card -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Activity Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h3 class="text-primary"><?php echo number_format($total_activities); ?></h3>
                        <p class="mb-0">Total Activities</p>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-success">
                            <?php
                            try {
                                $stmt = $db->query("SELECT COUNT(*) as count FROM activities WHERE start_time > NOW()");
                                echo number_format($stmt->fetch()['count']);
                            } catch (Exception $e) {
                                echo '0';
                            }
                            ?>
                        </h3>
                        <p class="mb-0">Upcoming</p>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-info">
                            <?php
                            try {
                                $stmt = $db->query("SELECT COUNT(*) as count FROM activity_participants WHERE status = 'joined'");
                                echo number_format($stmt->fetch()['count']);
                            } catch (Exception $e) {
                                echo '0';
                            }
                            ?>
                        </h3>
                        <p class="mb-0">Total Participants</p>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-warning">
                            <?php
                            try {
                                $stmt = $db->query("SELECT AVG(participant_count) as avg FROM (SELECT COUNT(*) as participant_count FROM activity_participants WHERE status = 'joined' GROUP BY activity_id) as subquery");
                                $avg = $stmt->fetch()['avg'];
                                echo $avg ? number_format($avg, 1) : '0';
                            } catch (Exception $e) {
                                echo '0';
                            }
                            ?>
                        </h3>
                        <p class="mb-0">Avg. Participants</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addActivity() {
    alert('Add activity functionality would be implemented here');
}

function editActivity(activityId) {
    alert('Edit activity functionality would be implemented here for activity ID: ' + activityId);
}

function viewParticipants(activityId) {
    alert('View participants functionality would be implemented here for activity ID: ' + activityId);
}
</script>

<?php include 'includes/footer.php'; ?>
