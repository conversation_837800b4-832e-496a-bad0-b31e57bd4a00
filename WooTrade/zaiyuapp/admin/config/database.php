<?php
/**
 * Database Configuration
 * Admin Panel for ZaiYu Dating App
 */

class Database {
    private $host = 'localhost';
    private $port = '5432';
    private $db_name = 'secondchance_db';
    private $username = 'secondchance';
    private $password = 'password';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "pgsql:host=" . $this->host . ";port=" . $this->port . ";dbname=" . $this->db_name;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * Admin Configuration
 */
class AdminConfig {
    // Admin credentials (in production, use hashed passwords and database storage)
    const ADMIN_USERNAME = 'admin';
    const ADMIN_PASSWORD = 'admin123'; // Change this in production!
    
    // Session configuration
    const SESSION_TIMEOUT = 3600; // 1 hour
    
    // Pagination
    const ITEMS_PER_PAGE = 20;
    
    // App settings
    const APP_NAME = 'ZaiYu Admin Panel';
    const APP_VERSION = '1.0.0';
}

/**
 * Utility Functions
 */
function formatDate($date) {
    return date('Y-m-d H:i:s', strtotime($date));
}

function formatAge($birthday) {
    $birthDate = new DateTime($birthday);
    $today = new DateTime('today');
    return $birthDate->diff($today)->y;
}

function getGenderIcon($gender) {
    return $gender === '男' ? '<i class="fas fa-mars text-primary"></i>' : '<i class="fas fa-venus text-danger"></i>';
}

function getStatusBadge($status) {
    switch($status) {
        case 'active':
            return '<span class="badge bg-success">Active</span>';
        case 'blocked':
            return '<span class="badge bg-danger">Blocked</span>';
        case 'pending':
            return '<span class="badge bg-warning">Pending</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

function showAlert($message, $type = 'info') {
    return "<div class='alert alert-$type alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>
