<?php
/**
 * MySQL Database Configuration (Alternative)
 * Admin Panel for ZaiYu Dating App
 */

class Database {
    private $host = 'localhost';
    private $port = '3306';
    private $db_name = 'your_mysql_database_name';  // Change this
    private $username = 'your_mysql_username';      // Change this
    private $password = 'your_mysql_password';      // Change this
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            // Check if MySQL PDO driver is available
            if (!in_array('mysql', PDO::getAvailableDrivers())) {
                throw new Exception("MySQL PDO driver is not installed. Available drivers: " . implode(', ', PDO::getAvailableDrivers()));
            }
            
            $dsn = "mysql:host=" . $this->host . ";port=" . $this->port . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        } catch(Exception $exception) {
            echo "Driver error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * Admin Configuration
 */
class AdminConfig {
    // Admin credentials (in production, use hashed passwords and database storage)
    const ADMIN_USERNAME = 'admin';
    const ADMIN_PASSWORD = 'admin123'; // Change this in production!
    
    // Session configuration
    const SESSION_TIMEOUT = 3600; // 1 hour
    
    // Pagination
    const ITEMS_PER_PAGE = 20;
    
    // App settings
    const APP_NAME = 'ZaiYu Admin Panel';
    const APP_VERSION = '1.0.0';
}

/**
 * Utility Functions
 */
function formatDate($date) {
    return date('Y-m-d H:i:s', strtotime($date));
}

function formatAge($birthday) {
    $birthDate = new DateTime($birthday);
    $today = new DateTime('today');
    return $birthDate->diff($today)->y;
}

function getGenderIcon($gender) {
    return $gender === '男' ? '<i class="fas fa-mars text-primary"></i>' : '<i class="fas fa-venus text-danger"></i>';
}

function getStatusBadge($status) {
    switch($status) {
        case 'active':
            return '<span class="badge bg-success">Active</span>';
        case 'blocked':
            return '<span class="badge bg-danger">Blocked</span>';
        case 'pending':
            return '<span class="badge bg-warning">Pending</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

function showAlert($message, $type = 'info') {
    return "<div class='alert alert-$type alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>

<!-- 
MySQL Database Schema for ZaiYu Admin Panel:

CREATE DATABASE secondchance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE secondchance_db;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    gender ENUM('男', '女') NOT NULL,
    birthday DATE,
    current_location VARCHAR(200),
    status ENUM('active', 'blocked', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

CREATE TABLE matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user1_id INT NOT NULL,
    user2_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user1_id) REFERENCES users(id),
    FOREIGN KEY (user2_id) REFERENCES users(id)
);

CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    match_id INT NOT NULL,
    sender_id INT NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(id),
    FOREIGN KEY (sender_id) REFERENCES users(id)
);

CREATE TABLE activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    location VARCHAR(200),
    start_time DATETIME NOT NULL,
    max_participants INT DEFAULT 10,
    creator_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id)
);

CREATE TABLE activity_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('joined', 'left', 'pending') DEFAULT 'joined',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE photos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
-->
