<?php
/**
 * OSS (Object Storage Service) Configuration
 * Admin Panel for ZaiYu Dating App
 */

class OSSConfig {
    // Aliyun OSS Configuration
    const OSS_ENDPOINT = 'https://oss-cn-shenzhen.aliyuncs.com'; // Change to your OSS region
    const OSS_BUCKET = 'zaiyuapp-photos'; // Change to your bucket name
    const OSS_ACCESS_KEY_ID = 'your-access-key-id'; // Change to your access key
    const OSS_ACCESS_KEY_SECRET = 'your-access-key-secret'; // Change to your secret key
    
    // Photo storage paths
    const PHOTO_PATH_PREFIX = 'user-photos/'; // Folder in OSS bucket
    const AVATAR_PATH_PREFIX = 'avatars/'; // Folder for avatar photos
    const TEMP_PATH_PREFIX = 'temp/'; // Folder for temporary uploads
    
    // Image settings
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB max file size
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    const THUMBNAIL_SIZES = [
        'small' => [150, 150],
        'medium' => [300, 300],
        'large' => [600, 600]
    ];
    
    /**
     * Get the full OSS URL for a photo
     * @param string $filename The filename stored in database
     * @return string Full URL to the photo
     */
    public static function getPhotoUrl($filename) {
        if (empty($filename)) {
            return self::getDefaultPhotoUrl();
        }
        
        // If filename already contains full URL, return as is
        if (strpos($filename, 'http') === 0) {
            return $filename;
        }
        
        // Build full OSS URL
        $bucket_url = str_replace('https://', 'https://' . self::OSS_BUCKET . '.', self::OSS_ENDPOINT);
        return $bucket_url . '/' . self::PHOTO_PATH_PREFIX . $filename;
    }
    
    /**
     * Get the full OSS URL for an avatar
     * @param string $filename The filename stored in database
     * @return string Full URL to the avatar
     */
    public static function getAvatarUrl($filename) {
        if (empty($filename)) {
            return self::getDefaultAvatarUrl();
        }
        
        // If filename already contains full URL, return as is
        if (strpos($filename, 'http') === 0) {
            return $filename;
        }
        
        // Build full OSS URL
        $bucket_url = str_replace('https://', 'https://' . self::OSS_BUCKET . '.', self::OSS_ENDPOINT);
        return $bucket_url . '/' . self::AVATAR_PATH_PREFIX . $filename;
    }
    
    /**
     * Get default photo URL when no photo exists
     * @return string Default photo URL
     */
    public static function getDefaultPhotoUrl() {
        return 'data:image/svg+xml;base64,' . base64_encode('
            <svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="300" height="300" fill="#F8F9FA"/>
                <circle cx="150" cy="120" r="40" fill="#DEE2E6"/>
                <path d="M80 220C80 180 110 150 150 150C190 150 220 180 220 220V250H80V220Z" fill="#DEE2E6"/>
                <text x="150" y="280" text-anchor="middle" fill="#6C757D" font-family="Arial" font-size="14">No Photo</text>
            </svg>
        ');
    }
    
    /**
     * Get default avatar URL when no avatar exists
     * @return string Default avatar URL
     */
    public static function getDefaultAvatarUrl() {
        return 'data:image/svg+xml;base64,' . base64_encode('
            <svg width="150" height="150" viewBox="0 0 150 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="150" height="150" fill="#6F42C1" rx="75"/>
                <circle cx="75" cy="60" r="25" fill="white"/>
                <path d="M40 110C40 85 55 70 75 70C95 70 110 85 110 110V125H40V110Z" fill="white"/>
            </svg>
        ');
    }
    
    /**
     * Get thumbnail URL with specified size
     * @param string $filename The filename stored in database
     * @param string $size Size key (small, medium, large)
     * @return string Full URL to the thumbnail
     */
    public static function getThumbnailUrl($filename, $size = 'medium') {
        if (empty($filename)) {
            return self::getDefaultPhotoUrl();
        }
        
        $base_url = self::getPhotoUrl($filename);
        
        // Add OSS image processing parameters for thumbnails
        if (isset(self::THUMBNAIL_SIZES[$size])) {
            $width = self::THUMBNAIL_SIZES[$size][0];
            $height = self::THUMBNAIL_SIZES[$size][1];
            return $base_url . '?x-oss-process=image/resize,m_fill,w_' . $width . ',h_' . $height;
        }
        
        return $base_url;
    }
    
    /**
     * Validate if file extension is allowed
     * @param string $filename The filename to check
     * @return bool True if extension is allowed
     */
    public static function isAllowedExtension($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, self::ALLOWED_EXTENSIONS);
    }
    
    /**
     * Generate a unique filename for upload
     * @param string $original_filename Original filename
     * @param int $user_id User ID for uniqueness
     * @return string Generated unique filename
     */
    public static function generateUniqueFilename($original_filename, $user_id) {
        $extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        return "user_{$user_id}_{$timestamp}_{$random}.{$extension}";
    }
    
    /**
     * Get OSS configuration for SDK
     * @return array OSS configuration array
     */
    public static function getOSSConfig() {
        return [
            'endpoint' => self::OSS_ENDPOINT,
            'bucket' => self::OSS_BUCKET,
            'access_key_id' => self::OSS_ACCESS_KEY_ID,
            'access_key_secret' => self::OSS_ACCESS_KEY_SECRET,
        ];
    }
}

// Helper functions for backward compatibility
function getPhotoUrl($filename) {
    return OSSConfig::getPhotoUrl($filename);
}

function getAvatarUrl($filename) {
    return OSSConfig::getAvatarUrl($filename);
}

function getThumbnailUrl($filename, $size = 'medium') {
    return OSSConfig::getThumbnailUrl($filename, $size);
}
?>
