<?php
/**
 * Admin Dashboard
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'Dashboard';
$database = new Database();
$db = $database->getConnection();

// Get statistics
$stats = [];

try {
    // Total users
    $stmt = $db->query("SELECT COUNT(*) as total FROM users");
    $stats['total_users'] = $stmt->fetch()['total'];
    
    // Active users (logged in within last 30 days)
    $stmt = $db->query("SELECT COUNT(*) as active FROM users WHERE updated_at >= NOW() - INTERVAL '30 days'");
    $stats['active_users'] = $stmt->fetch()['active'];
    
    // New users today
    $stmt = $db->query("SELECT COUNT(*) as new_today FROM users WHERE DATE(created_at) = CURRENT_DATE");
    $stats['new_users_today'] = $stmt->fetch()['new_today'];
    
    // Total matches
    $stmt = $db->query("SELECT COUNT(*) as total FROM matches");
    $stats['total_matches'] = $stmt->fetch()['total'];
    
    // Total messages
    $stmt = $db->query("SELECT COUNT(*) as total FROM messages");
    $stats['total_messages'] = $stmt->fetch()['total'];
    
    // Total activities
    $stmt = $db->query("SELECT COUNT(*) as total FROM activities");
    $stats['total_activities'] = $stmt->fetch()['total'];
    
    // Gender distribution
    $stmt = $db->query("SELECT gender, COUNT(*) as count FROM users GROUP BY gender");
    $gender_stats = $stmt->fetchAll();
    
    // Recent users
    $stmt = $db->query("SELECT id, name, phone, gender, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    $recent_users = $stmt->fetchAll();
    
    // User registration trend (last 7 days)
    $stmt = $db->query("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM users 
        WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $registration_trend = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error fetching statistics: " . $e->getMessage();
}

include 'includes/header.php';
?>

<!-- Dashboard Content -->
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard Overview
        </h1>
    </div>
</div>

<?php if (isset($error_message)): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo number_format($stats['total_users'] ?? 0); ?></h3>
                    <p class="mb-0">Total Users</p>
                </div>
                <i class="fas fa-users fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo number_format($stats['active_users'] ?? 0); ?></h3>
                    <p class="mb-0">Active Users</p>
                </div>
                <i class="fas fa-user-check fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo number_format($stats['total_matches'] ?? 0); ?></h3>
                    <p class="mb-0">Total Matches</p>
                </div>
                <i class="fas fa-heart fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo number_format($stats['new_users_today'] ?? 0); ?></h3>
                    <p class="mb-0">New Today</p>
                </div>
                <i class="fas fa-user-plus fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Registration Trend Chart -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> User Registration Trend (Last 7 Days)</h5>
            </div>
            <div class="card-body">
                <canvas id="registrationChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Gender Distribution -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Gender Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="genderChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Users and Quick Stats -->
<div class="row">
    <!-- Recent Users -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-user-clock"></i> Recent Users</h5>
                <a href="users.php" class="btn btn-sm btn-outline-light">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Gender</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($recent_users)): ?>
                                <?php foreach ($recent_users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['name']); ?></td>
                                        <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                        <td><?php echo getGenderIcon($user['gender']) . ' ' . $user['gender']; ?></td>
                                        <td><?php echo formatDate($user['created_at']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No users found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Messages</span>
                    <strong><?php echo number_format($stats['total_messages'] ?? 0); ?></strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Activities</span>
                    <strong><?php echo number_format($stats['total_activities'] ?? 0); ?></strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Match Rate</span>
                    <strong>
                        <?php 
                        $match_rate = $stats['total_users'] > 0 ? 
                            round(($stats['total_matches'] / $stats['total_users']) * 100, 1) : 0;
                        echo $match_rate . '%';
                        ?>
                    </strong>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">Last updated: <?php echo date('Y-m-d H:i:s'); ?></small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Registration Trend Chart
const registrationData = <?php echo json_encode($registration_trend); ?>;
const registrationLabels = registrationData.map(item => item.date);
const registrationCounts = registrationData.map(item => parseInt(item.count));

const registrationCtx = document.getElementById('registrationChart').getContext('2d');
new Chart(registrationCtx, {
    type: 'line',
    data: {
        labels: registrationLabels,
        datasets: [{
            label: 'New Users',
            data: registrationCounts,
            borderColor: '#6f42c1',
            backgroundColor: 'rgba(111, 66, 193, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Gender Distribution Chart
const genderData = <?php echo json_encode($gender_stats); ?>;
const genderLabels = genderData.map(item => item.gender);
const genderCounts = genderData.map(item => parseInt(item.count));

const genderCtx = document.getElementById('genderChart').getContext('2d');
new Chart(genderCtx, {
    type: 'doughnut',
    data: {
        labels: genderLabels,
        datasets: [{
            data: genderCounts,
            backgroundColor: ['#007bff', '#dc3545'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
