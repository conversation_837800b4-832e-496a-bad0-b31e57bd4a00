<?php
/**
 * Debug Photos - Check photo data and URLs
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
if (!Auth::isLoggedIn()) {
    redirectTo('login.php');
}

$database = new Database();
$db = $database->getConnection();

echo "<h2>Photo Debug Information</h2>";

try {
    // Check if photos table exists
    $stmt = $db->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'photos'");
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "<div class='alert alert-danger'>❌ Photos table does not exist!</div>";
        echo "<p>Run the fix_photos_schema.sql script first.</p>";
        exit;
    }
    
    echo "<div class='alert alert-success'>✅ Photos table exists</div>";
    
    // Check table structure
    echo "<h3>Table Structure:</h3>";
    $stmt = $db->query("SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_name = 'photos' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Nullable</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['column_name']) . "</td>";
        echo "<td>" . htmlspecialchars($col['data_type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['is_nullable']) . "</td>";
        echo "<td>" . htmlspecialchars($col['column_default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check total photos
    $stmt = $db->query("SELECT COUNT(*) as total FROM photos");
    $total = $stmt->fetch()['total'];
    echo "<h3>Total Photos: {$total}</h3>";
    
    if ($total == 0) {
        echo "<div class='alert alert-warning'>⚠️ No photos found in database!</div>";
        echo "<p>You need to add some sample photos. Run this SQL:</p>";
        echo "<pre>";
        echo "INSERT INTO photos (user_id, filename, is_main, created_at) VALUES\n";
        echo "(1, 'user_1_sample.jpg', TRUE, CURRENT_TIMESTAMP),\n";
        echo "(1, 'user_1_sample2.jpg', FALSE, CURRENT_TIMESTAMP),\n";
        echo "(2, 'user_2_sample.jpg', TRUE, CURRENT_TIMESTAMP);";
        echo "</pre>";
    } else {
        // Show sample photos
        echo "<h3>Sample Photos:</h3>";
        $stmt = $db->query("SELECT p.*, u.name as user_name FROM photos p LEFT JOIN users u ON p.user_id = u.id ORDER BY p.id LIMIT 10");
        $photos = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><th>ID</th><th>User</th><th>Filename</th><th>Is Main</th><th>Created</th><th>Generated URL</th></tr>";
        
        foreach ($photos as $photo) {
            // Generate URL using the same logic as edit_user.php
            $oss_bucket = 'zaiyuapp-photos';
            $oss_endpoint = 'oss-cn-shenzhen.aliyuncs.com';
            $generated_url = "https://{$oss_bucket}.{$oss_endpoint}/user-photos/{$photo['filename']}";
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($photo['id']) . "</td>";
            echo "<td>" . htmlspecialchars($photo['user_name'] ?? 'Unknown') . "</td>";
            echo "<td>" . htmlspecialchars($photo['filename']) . "</td>";
            echo "<td>" . ($photo['is_main'] ? '✅' : '❌') . "</td>";
            echo "<td>" . htmlspecialchars($photo['created_at']) . "</td>";
            echo "<td style='font-size: 10px; word-break: break-all;'>" . htmlspecialchars($generated_url) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test first photo URL
        if (!empty($photos)) {
            $test_photo = $photos[0];
            $test_url = "https://{$oss_bucket}.{$oss_endpoint}/user-photos/{$test_photo['filename']}";
            
            echo "<h3>URL Test:</h3>";
            echo "<p><strong>Testing URL:</strong> <code>{$test_url}</code></p>";
            echo "<img src='{$test_url}' alt='Test Photo' style='max-width: 200px; border: 1px solid #ccc;' ";
            echo "onload=\"document.getElementById('test-result').innerHTML='✅ Image loaded successfully!'\" ";
            echo "onerror=\"document.getElementById('test-result').innerHTML='❌ Image failed to load - check OSS settings and file existence'\">";
            echo "<div id='test-result' style='margin-top: 10px; padding: 10px; background: #f0f0f0;'>Loading...</div>";
        }
    }
    
    // Show users with photo counts
    echo "<h3>Users with Photo Counts:</h3>";
    $stmt = $db->query("
        SELECT u.id, u.name, COUNT(p.id) as photo_count, 
               COUNT(CASE WHEN p.is_main THEN 1 END) as main_photos
        FROM users u 
        LEFT JOIN photos p ON u.id = p.user_id 
        GROUP BY u.id, u.name 
        ORDER BY photo_count DESC 
        LIMIT 10
    ");
    $user_stats = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User ID</th><th>Name</th><th>Total Photos</th><th>Main Photos</th></tr>";
    foreach ($user_stats as $stat) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($stat['id']) . "</td>";
        echo "<td>" . htmlspecialchars($stat['name']) . "</td>";
        echo "<td>" . htmlspecialchars($stat['photo_count']) . "</td>";
        echo "<td>" . htmlspecialchars($stat['main_photos']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<br><a href='users.php'>← Back to Users</a>";
?>
