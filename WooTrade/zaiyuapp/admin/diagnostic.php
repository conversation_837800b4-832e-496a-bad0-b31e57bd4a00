<?php
/**
 * Diagnostic Page
 * Check server configuration and database connectivity
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>ZaiYu Admin Panel - Server Diagnostic</h1>";
echo "<hr>";

// PHP Version
echo "<h2>PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";

// PDO Drivers
echo "<h2>PDO Drivers Available</h2>";
if (class_exists('PDO')) {
    $drivers = PDO::getAvailableDrivers();
    if (!empty($drivers)) {
        echo "<ul>";
        foreach ($drivers as $driver) {
            echo "<li>$driver</li>";
        }
        echo "</ul>";
        
        if (in_array('pgsql', $drivers)) {
            echo "<p style='color: green;'><strong>✅ PostgreSQL PDO driver is available!</strong></p>";
        } else {
            echo "<p style='color: red;'><strong>❌ PostgreSQL PDO driver is NOT available!</strong></p>";
            echo "<p><strong>To install PostgreSQL PDO driver:</strong></p>";
            echo "<ul>";
            echo "<li><strong>Ubuntu/Debian:</strong> <code>sudo apt-get install php-pgsql</code></li>";
            echo "<li><strong>CentOS/RHEL:</strong> <code>sudo yum install php-pgsql</code></li>";
            echo "<li><strong>Windows XAMPP:</strong> Uncomment <code>extension=pdo_pgsql</code> in php.ini</li>";
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>No PDO drivers found!</p>";
    }
} else {
    echo "<p style='color: red;'>PDO class not found!</p>";
}

// Extensions
echo "<h2>PHP Extensions</h2>";
$required_extensions = ['pdo', 'pdo_pgsql', 'pgsql', 'session', 'json'];
echo "<ul>";
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? "✅ Loaded" : "❌ Not loaded";
    $color = $loaded ? "green" : "red";
    echo "<li style='color: $color;'>$ext - $status</li>";
}
echo "</ul>";

// Database Connection Test
echo "<h2>Database Connection Test</h2>";

// Try to include the database config
try {
    require_once 'config/database.php';
    echo "<p>✅ Database config loaded successfully</p>";
    
    // Test connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
        
        // Test a simple query
        try {
            $stmt = $conn->query("SELECT version()");
            $version = $stmt->fetch();
            echo "<p><strong>PostgreSQL Version:</strong> " . $version['version'] . "</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Connected but query failed: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading database config: " . $e->getMessage() . "</p>";
}

// File Permissions
echo "<h2>File Permissions</h2>";
$files_to_check = [
    'config/database.php',
    'includes/auth.php',
    'includes/header.php',
    'dashboard.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "<p>$file - Permissions: $perms ✅</p>";
    } else {
        echo "<p style='color: red;'>$file - File not found ❌</p>";
    }
}

// Session Test
echo "<h2>Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p style='color: green;'>✅ Session is active</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Session not started</p>";
}

// Alternative Database Solutions
echo "<h2>Alternative Solutions</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>If PostgreSQL PDO is not available, you have these options:</h3>";
echo "<ol>";
echo "<li><strong>Install PostgreSQL PDO extension</strong> (Recommended)";
echo "<ul>";
echo "<li>Ubuntu/Debian: <code>sudo apt-get install php-pgsql && sudo systemctl restart apache2</code></li>";
echo "<li>CentOS/RHEL: <code>sudo yum install php-pgsql && sudo systemctl restart httpd</code></li>";
echo "<li>Windows: Enable in php.ini and restart server</li>";
echo "</ul></li>";
echo "<li><strong>Use MySQL instead</strong> - I can modify the admin panel to use MySQL</li>";
echo "<li><strong>Use SQLite</strong> - Lightweight file-based database option</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Admin Panel</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
code { background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
