<?php
/**
 * Edit User Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'config/oss.php';

// Check if user is logged in
if (!Auth::isLoggedIn()) {
    redirectTo('login.php');
}

$page_title = 'Edit User';
$database = new Database();
$db = $database->getConnection();

$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$success_message = $_GET['success'] ?? '';
$error_message = $_GET['error'] ?? '';

if (!$user_id) {
    redirectTo('users.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $name = sanitizeInput($_POST['name']);
        $phone = sanitizeInput($_POST['phone']);
        $gender = sanitizeInput($_POST['gender']);
        $birthday = sanitizeInput($_POST['birthday']);
        $current_location = sanitizeInput($_POST['current_location']);
        $status = sanitizeInput($_POST['status']);
        
        // Validate required fields
        if (empty($name) || empty($phone) || empty($gender)) {
            throw new Exception('Name, phone, and gender are required fields.');
        }
        
        // Validate phone format (basic validation)
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            throw new Exception('Please enter a valid Chinese phone number.');
        }
        
        // Check if phone number is already taken by another user
        $stmt = $db->prepare("SELECT id FROM users WHERE phone = ? AND id != ?");
        $stmt->execute([$phone, $user_id]);
        if ($stmt->fetch()) {
            throw new Exception('This phone number is already registered by another user.');
        }
        
        // Update user
        $stmt = $db->prepare("
            UPDATE users 
            SET name = ?, phone = ?, gender = ?, birthday = ?, current_location = ?, status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([$name, $phone, $gender, $birthday ?: null, $current_location, $status, $user_id]);
        
        $success_message = "User updated successfully!";
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Fetch user data
try {
    $stmt = $db->prepare("
        SELECT id, name, phone, gender, birthday, current_location, status, created_at, updated_at,
               (SELECT COUNT(*) FROM photos WHERE user_id = users.id) as photo_count,
               (SELECT COUNT(*) FROM matches WHERE user1_id = users.id OR user2_id = users.id) as match_count
        FROM users
        WHERE id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        redirectTo('users.php');
    }

    // Fetch user photos with full OSS path
    $stmt = $db->prepare("
        SELECT id, filename, is_main, created_at
        FROM photos
        WHERE user_id = ?
        ORDER BY is_main DESC, created_at DESC
    ");
    $stmt->execute([$user_id]);
    $photos = $stmt->fetchAll();

    // Add OSS full URL to photos
    foreach ($photos as &$photo) {
        $photo['full_url'] = OSSConfig::getPhotoUrl($photo['filename']);
        $photo['thumbnail_url'] = OSSConfig::getThumbnailUrl($photo['filename'], 'medium');
    }

} catch (Exception $e) {
    $error_message = "Error fetching user data: " . $e->getMessage();
    $user = null;
    $photos = [];
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-edit"></i> Edit User</h2>
                <a href="users.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Users
                </a>
            </div>
            
            <?php if ($success_message): ?>
                <?php echo showAlert($success_message, 'success'); ?>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <?php echo showAlert($error_message, 'danger'); ?>
            <?php endif; ?>
            
            <?php if ($user): ?>
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user"></i> User Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid name.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($user['phone']); ?>" 
                                                   pattern="1[3-9]\d{9}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid Chinese phone number (11 digits starting with 1).
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gender" class="form-label">Gender *</label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="">Select Gender</option>
                                                <option value="男" <?php echo $user['gender'] === '男' ? 'selected' : ''; ?>>男 (Male)</option>
                                                <option value="女" <?php echo $user['gender'] === '女' ? 'selected' : ''; ?>>女 (Female)</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a gender.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="birthday" class="form-label">Birthday</label>
                                            <input type="date" class="form-control" id="birthday" name="birthday"
                                                   value="<?php echo $user['birthday'] ? date('Y-m-d', strtotime($user['birthday'])) : ''; ?>"
                                                   max="<?php echo date('Y-m-d', strtotime('-18 years')); ?>">
                                            <small class="form-text text-muted">Must be at least 18 years old</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="current_location" class="form-label">Current Location</label>
                                            <input type="text" class="form-control" id="current_location" name="current_location" 
                                                   value="<?php echo htmlspecialchars($user['current_location'] ?? ''); ?>" 
                                                   placeholder="e.g., 深圳市南山区">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Account Status *</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="blocked" <?php echo $user['status'] === 'blocked' ? 'selected' : ''; ?>>Blocked</option>
                                                <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update User
                                    </button>
                                    <a href="users.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> User Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary"><?php echo $user['photo_count']; ?></h4>
                                        <small class="text-muted">Photos</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success"><?php echo $user['match_count']; ?></h4>
                                    <small class="text-muted">Matches</small>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="mb-2">
                                <small class="text-muted">User ID:</small>
                                <span class="float-end">#<?php echo $user['id']; ?></span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Registered:</small>
                                <span class="float-end"><?php echo formatDate($user['created_at']); ?></span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Last Updated:</small>
                                <span class="float-end"><?php echo formatDate($user['updated_at']); ?></span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Age:</small>
                                <span class="float-end">
                                    <?php echo $user['birthday'] ? formatAge($user['birthday']) . ' years' : 'N/A'; ?>
                                </span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Status:</small>
                                <span class="float-end"><?php echo getStatusBadge($user['status']); ?></span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Gender:</small>
                                <span class="float-end"><?php echo getGenderIcon($user['gender']) . ' ' . $user['gender']; ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs"></i> Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <?php if ($user['status'] === 'active'): ?>
                                    <a href="users.php?action=block&id=<?php echo $user['id']; ?>" 
                                       class="btn btn-warning btn-sm" 
                                       onclick="return confirm('Are you sure you want to block this user?')">
                                        <i class="fas fa-ban"></i> Block User
                                    </a>
                                <?php else: ?>
                                    <a href="users.php?action=unblock&id=<?php echo $user['id']; ?>" 
                                       class="btn btn-success btn-sm">
                                        <i class="fas fa-check"></i> Unblock User
                                    </a>
                                <?php endif; ?>
                                
                                <a href="users.php?action=delete&id=<?php echo $user['id']; ?>" 
                                   class="btn btn-danger btn-sm" 
                                   onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')">
                                    <i class="fas fa-trash"></i> Delete User
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Photos Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-images"></i> User Photos (<?php echo count($photos); ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($photos)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">This user has not uploaded any photos yet.</p>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($photos as $photo): ?>
                                        <div class="col-md-3 col-sm-4 col-6 mb-3">
                                            <div class="card photo-card">
                                                <div class="position-relative">
                                                    <!-- Actual photo from OSS -->
                                                    <img src="<?php echo htmlspecialchars($photo['thumbnail_url']); ?>"
                                                         alt="User Photo"
                                                         class="photo-image"
                                                         data-full-url="<?php echo htmlspecialchars($photo['full_url']); ?>"
                                                         onclick="openPhotoModal(this)"
                                                         onerror="this.onerror=null; this.src='<?php echo OSSConfig::getDefaultPhotoUrl(); ?>'; this.parentElement.querySelector('.photo-loading').style.display='none';">

                                                    <!-- Loading overlay -->
                                                    <div class="photo-loading position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                                        <div class="spinner-border text-primary" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                    </div>

                                                    <!-- Main photo badge -->
                                                    <?php if ($photo['is_main']): ?>
                                                        <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                                                            <i class="fas fa-star"></i> Main
                                                        </span>
                                                    <?php endif; ?>

                                                    <!-- Photo actions -->
                                                    <div class="photo-actions position-absolute top-0 end-0 m-2">
                                                        <div class="btn-group-vertical">
                                                            <?php if (!$photo['is_main']): ?>
                                                                <button class="btn btn-sm btn-success"
                                                                        onclick="setMainPhoto(<?php echo $photo['id']; ?>)"
                                                                        title="Set as main photo">
                                                                    <i class="fas fa-star"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <button class="btn btn-sm btn-danger"
                                                                    onclick="deletePhoto(<?php echo $photo['id']; ?>)"
                                                                    title="Delete photo">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-body p-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar"></i>
                                                        <?php echo formatDate($photo['created_at']); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Photo Statistics -->
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h5 class="text-primary"><?php echo count($photos); ?></h5>
                                            <small class="text-muted">Total Photos</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h5 class="text-success">
                                                <?php echo count(array_filter($photos, function($p) { return $p['is_main']; })); ?>
                                            </h5>
                                            <small class="text-muted">Main Photos</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h5 class="text-info">
                                                <?php
                                                $recent_photos = array_filter($photos, function($p) {
                                                    return strtotime($p['created_at']) > strtotime('-30 days');
                                                });
                                                echo count($recent_photos);
                                                ?>
                                            </h5>
                                            <small class="text-muted">Recent (30 days)</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Photo Modal -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">User Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" alt="User Photo" class="img-fluid rounded">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="downloadPhoto()">
                    <i class="fas fa-download"></i> Download
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.photo-card {
    transition: transform 0.2s;
    overflow: hidden;
}

.photo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.photo-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
    transition: transform 0.3s ease;
}

.photo-card:hover .photo-image {
    transform: scale(1.05);
}

.photo-placeholder {
    height: 200px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
}

.photo-error {
    height: 200px;
    background: #f8f9fa;
    border: 2px dashed #ffc107;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.photo-loading {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px 8px 0 0;
    z-index: 1;
}

.photo-actions {
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 2;
}

.photo-card:hover .photo-actions {
    opacity: 1;
}

.photo-actions .btn {
    margin-bottom: 2px;
    backdrop-filter: blur(5px);
    background: rgba(0, 0, 0, 0.7);
    border: none;
}

.photo-actions .btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Hide loading spinner when image loads */
.photo-image:not([src=""]) + .photo-loading {
    display: none;
}
</style>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Phone number formatting
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
});

// Image loading handlers
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('.photo-image');

    images.forEach(function(img) {
        // Hide loading spinner when image loads successfully
        img.addEventListener('load', function() {
            const loadingDiv = this.nextElementSibling;
            if (loadingDiv && loadingDiv.classList.contains('photo-loading')) {
                loadingDiv.style.display = 'none';
            }
        });

        // Handle image load errors
        img.addEventListener('error', function() {
            const loadingDiv = this.nextElementSibling;
            if (loadingDiv && loadingDiv.classList.contains('photo-loading')) {
                loadingDiv.style.display = 'none';
            }
        });
    });
});

// Photo management functions
function setMainPhoto(photoId) {
    if (confirm('Set this as the main photo?')) {
        // Create a form to submit the action
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'photo_actions.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'set_main';

        const photoInput = document.createElement('input');
        photoInput.type = 'hidden';
        photoInput.name = 'photo_id';
        photoInput.value = photoId;

        const userInput = document.createElement('input');
        userInput.type = 'hidden';
        userInput.name = 'user_id';
        userInput.value = '<?php echo $user_id; ?>';

        form.appendChild(actionInput);
        form.appendChild(photoInput);
        form.appendChild(userInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function deletePhoto(photoId) {
    if (confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
        // Create a form to submit the action
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'photo_actions.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';

        const photoInput = document.createElement('input');
        photoInput.type = 'hidden';
        photoInput.name = 'photo_id';
        photoInput.value = photoId;

        const userInput = document.createElement('input');
        userInput.type = 'hidden';
        userInput.name = 'user_id';
        userInput.value = '<?php echo $user_id; ?>';

        form.appendChild(actionInput);
        form.appendChild(photoInput);
        form.appendChild(userInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Photo modal functions
function openPhotoModal(imgElement) {
    const fullUrl = imgElement.getAttribute('data-full-url');
    const modalPhoto = document.getElementById('modalPhoto');
    modalPhoto.src = fullUrl;

    // Store current photo URL for download
    window.currentPhotoUrl = fullUrl;

    // Show modal
    const photoModal = new bootstrap.Modal(document.getElementById('photoModal'));
    photoModal.show();
}

function downloadPhoto() {
    if (window.currentPhotoUrl) {
        // Create a temporary link to download the image
        const link = document.createElement('a');
        link.href = window.currentPhotoUrl;
        link.download = 'user_photo_' + Date.now() + '.jpg';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
</script>

<?php include 'includes/footer.php'; ?>
