-- Fix Activities Table Schema for ZaiYu Admin Panel
-- Run this to add missing columns and tables for activities functionality

-- Connect to your database first:
-- sudo -u postgres psql secondchance_db

-- Add missing columns to activities table
DO $$
BEGIN
    -- Add creator_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'creator_id') THEN
        ALTER TABLE activities ADD COLUMN creator_id INTEGER;
        -- Add foreign key constraint if users table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
            ALTER TABLE activities ADD CONSTRAINT fk_activities_creator 
            FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE SET NULL;
        END IF;
        CREATE INDEX IF NOT EXISTS idx_activities_creator_id ON activities(creator_id);
    END IF;
    
    -- Add title column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'title') THEN
        ALTER TABLE activities ADD COLUMN title VARCHAR(200) NOT NULL DEFAULT 'Untitled Activity';
    END IF;
    
    -- Add description column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'description') THEN
        ALTER TABLE activities ADD COLUMN description TEXT;
    END IF;
    
    -- Add category column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'category') THEN
        ALTER TABLE activities ADD COLUMN category VARCHAR(100);
    END IF;
    
    -- Add location column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'location') THEN
        ALTER TABLE activities ADD COLUMN location VARCHAR(200);
    END IF;
    
    -- Add start_time column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'start_time') THEN
        ALTER TABLE activities ADD COLUMN start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;
    
    -- Add max_participants column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'max_participants') THEN
        ALTER TABLE activities ADD COLUMN max_participants INTEGER DEFAULT 10;
    END IF;
    
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'activities' AND column_name = 'created_at') THEN
        ALTER TABLE activities ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);
    END IF;
END $$;

-- Create activity_participants table if it doesn't exist
CREATE TABLE IF NOT EXISTS activity_participants (
    id SERIAL PRIMARY KEY,
    activity_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'joined',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(activity_id, user_id)
);

-- Add foreign key constraints for activity_participants if they don't exist
DO $$
BEGIN
    -- Add foreign key to activities table
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'fk_activity_participants_activity') THEN
        ALTER TABLE activity_participants 
        ADD CONSTRAINT fk_activity_participants_activity 
        FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE;
    END IF;
    
    -- Add foreign key to users table if users table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE constraint_name = 'fk_activity_participants_user') THEN
            ALTER TABLE activity_participants 
            ADD CONSTRAINT fk_activity_participants_user 
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- Create indexes for activity_participants
CREATE INDEX IF NOT EXISTS idx_activity_participants_activity_id ON activity_participants(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON activity_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_status ON activity_participants(status);
CREATE INDEX IF NOT EXISTS idx_activity_participants_joined_at ON activity_participants(joined_at);

-- Add some sample activities if the table is empty
DO $$
DECLARE
    first_user_id INTEGER;
BEGIN
    -- Get the first user ID if users exist
    SELECT id INTO first_user_id FROM users LIMIT 1;
    
    -- Add sample activities if activities table is empty
    IF NOT EXISTS (SELECT 1 FROM activities LIMIT 1) THEN
        INSERT INTO activities (title, description, category, location, start_time, max_participants, creator_id)
        VALUES 
        ('周末爬山', '一起去梧桐山爬山，锻炼身体，认识朋友', '户外运动', '深圳梧桐山', CURRENT_TIMESTAMP + INTERVAL '7 days', 10, first_user_id),
        ('咖啡聚会', '在星巴克喝咖啡聊天，轻松社交', '社交聚会', '海岸城星巴克', CURRENT_TIMESTAMP + INTERVAL '3 days', 6, first_user_id),
        ('电影之夜', '一起看最新上映的电影', '娱乐', '万象城影院', CURRENT_TIMESTAMP + INTERVAL '5 days', 8, first_user_id),
        ('读书分享会', '分享最近读过的好书', '文化', '深圳图书馆', CURRENT_TIMESTAMP + INTERVAL '10 days', 12, first_user_id),
        ('美食探索', '尝试新开的餐厅', '美食', '南山区', CURRENT_TIMESTAMP + INTERVAL '2 days', 4, first_user_id);
        
        -- Add some sample participants
        IF first_user_id IS NOT NULL THEN
            INSERT INTO activity_participants (activity_id, user_id, status)
            SELECT a.id, first_user_id, 'joined'
            FROM activities a
            WHERE a.creator_id = first_user_id
            LIMIT 3;
        END IF;
    END IF;
END $$;

-- Show results
SELECT 'Activities schema updated successfully!' as message;

SELECT 'Activities table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'activities' 
ORDER BY ordinal_position;

SELECT 'Activity_participants table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'activity_participants' 
ORDER BY ordinal_position;

SELECT 'Total activities:' as info, COUNT(*) as count FROM activities;
SELECT 'Total activity participants:' as info, COUNT(*) as count FROM activity_participants;
