-- Fix PostgreSQL Database Schema for ZaiYu Admin Panel
-- This script adds missing columns to existing tables

-- Connect to your database first:
-- psql -U secondchance -d secondchance_db

-- Add missing columns to users table
DO $$
BEGIN
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'status') THEN
        ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'active';
        CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
    END IF;
    
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'created_at') THEN
        ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
    END IF;
    
    -- Add updated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'updated_at') THEN
        ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        CREATE INDEX IF NOT EXISTS idx_users_updated_at ON users(updated_at);
    END IF;
    
    -- Add deleted_at column if it doesn't exist (for soft deletes)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'deleted_at') THEN
        ALTER TABLE users ADD COLUMN deleted_at TIMESTAMP NULL;
        CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);
    END IF;
    
    -- Add current_location column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'current_location') THEN
        ALTER TABLE users ADD COLUMN current_location VARCHAR(200);
    END IF;
END $$;

-- Create matches table if it doesn't exist
CREATE TABLE IF NOT EXISTS matches (
    id SERIAL PRIMARY KEY,
    user1_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user2_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user1_id, user2_id)
);
CREATE INDEX IF NOT EXISTS idx_matches_user1 ON matches(user1_id);
CREATE INDEX IF NOT EXISTS idx_matches_user2 ON matches(user2_id);
CREATE INDEX IF NOT EXISTS idx_matches_created_at ON matches(created_at);

-- Create messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    match_id INTEGER NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
    sender_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_messages_match_id ON messages(match_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- Create activities table if it doesn't exist
CREATE TABLE IF NOT EXISTS activities (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    location VARCHAR(200),
    start_time TIMESTAMP NOT NULL,
    max_participants INTEGER DEFAULT 10,
    creator_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_activities_start_time ON activities(start_time);
CREATE INDEX IF NOT EXISTS idx_activities_category ON activities(category);
CREATE INDEX IF NOT EXISTS idx_activities_creator_id ON activities(creator_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);

-- Create activity_participants table if it doesn't exist
CREATE TABLE IF NOT EXISTS activity_participants (
    id SERIAL PRIMARY KEY,
    activity_id INTEGER NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'joined',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(activity_id, user_id)
);
CREATE INDEX IF NOT EXISTS idx_activity_participants_activity_id ON activity_participants(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON activity_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_status ON activity_participants(status);

-- Create photos table if it doesn't exist
CREATE TABLE IF NOT EXISTS photos (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_is_main ON photos(is_main);

-- Update existing users to have proper status values
UPDATE users SET status = 'active' WHERE status IS NULL OR status = '';

-- Add some sample data for testing (only if tables are empty)
DO $$
BEGIN
    -- Add sample matches if matches table is empty
    IF NOT EXISTS (SELECT 1 FROM matches LIMIT 1) THEN
        INSERT INTO matches (user1_id, user2_id) 
        SELECT u1.id, u2.id 
        FROM users u1, users u2 
        WHERE u1.id < u2.id 
        AND u1.gender != u2.gender 
        LIMIT 5;
    END IF;
    
    -- Add sample activities if activities table is empty
    IF NOT EXISTS (SELECT 1 FROM activities LIMIT 1) THEN
        INSERT INTO activities (title, description, category, location, start_time, max_participants, creator_id)
        SELECT 
            '周末聚会', 
            '一起聊天交友，认识新朋友', 
            '社交聚会', 
            '深圳市南山区', 
            CURRENT_TIMESTAMP + INTERVAL '7 days', 
            10,
            id
        FROM users 
        LIMIT 1;
    END IF;
END $$;

-- Show results
SELECT 'Database schema updated successfully!' as message;
SELECT 'Users table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

SELECT 'Total users:' as info, COUNT(*) as count FROM users;
SELECT 'Total matches:' as info, COUNT(*) as count FROM matches;
SELECT 'Total activities:' as info, COUNT(*) as count FROM activities;
