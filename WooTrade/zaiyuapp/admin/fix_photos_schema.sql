-- Fix Photos Table Schema for ZaiYu Admin Panel
-- Run this to add missing columns and tables for photos functionality

-- Connect to your database first:
-- sudo -u postgres psql secondchance_db

-- Create photos table if it doesn't exist
CREATE TABLE IF NOT EXISTS photos (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add missing columns to photos table if they don't exist
DO $$
BEGIN
    -- Add user_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'user_id') THEN
        ALTER TABLE photos ADD COLUMN user_id INTEGER NOT NULL DEFAULT 1;
        CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
    END IF;
    
    -- Add filename column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'filename') THEN
        ALTER TABLE photos ADD COLUMN filename VARCHAR(255) NOT NULL DEFAULT 'default.jpg';
    END IF;
    
    -- Add is_main column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'is_main') THEN
        ALTER TABLE photos ADD COLUMN is_main BOOLEAN DEFAULT FALSE;
        CREATE INDEX IF NOT EXISTS idx_photos_is_main ON photos(is_main);
    END IF;
    
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'created_at') THEN
        ALTER TABLE photos ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        CREATE INDEX IF NOT EXISTS idx_photos_created_at ON photos(created_at);
    END IF;
END $$;

-- Add foreign key constraint if users table exists and constraint doesn't exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE constraint_name = 'fk_photos_user') THEN
            ALTER TABLE photos 
            ADD CONSTRAINT fk_photos_user 
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- Add some sample photos for existing users (only if photos table is empty)
DO $$
DECLARE
    user_record RECORD;
    photo_count INTEGER := 0;
BEGIN
    -- Check if photos table is empty
    SELECT COUNT(*) INTO photo_count FROM photos;
    
    IF photo_count = 0 THEN
        -- Add sample photos for each user
        FOR user_record IN SELECT id, name, gender FROM users LIMIT 10 LOOP
            -- Add 2-4 photos per user
            INSERT INTO photos (user_id, filename, is_main, created_at) VALUES
            (user_record.id, user_record.name || '_photo_1.jpg', TRUE, CURRENT_TIMESTAMP - INTERVAL '30 days'),
            (user_record.id, user_record.name || '_photo_2.jpg', FALSE, CURRENT_TIMESTAMP - INTERVAL '25 days'),
            (user_record.id, user_record.name || '_photo_3.jpg', FALSE, CURRENT_TIMESTAMP - INTERVAL '20 days');
            
            -- Add one more photo for some users
            IF user_record.id % 2 = 0 THEN
                INSERT INTO photos (user_id, filename, is_main, created_at) VALUES
                (user_record.id, user_record.name || '_photo_4.jpg', FALSE, CURRENT_TIMESTAMP - INTERVAL '15 days');
            END IF;
        END LOOP;
    END IF;
END $$;

-- Ensure each user has only one main photo
DO $$
DECLARE
    user_record RECORD;
    main_photo_count INTEGER;
BEGIN
    FOR user_record IN SELECT DISTINCT user_id FROM photos LOOP
        -- Count main photos for this user
        SELECT COUNT(*) INTO main_photo_count 
        FROM photos 
        WHERE user_id = user_record.user_id AND is_main = TRUE;
        
        -- If no main photo, set the first photo as main
        IF main_photo_count = 0 THEN
            UPDATE photos 
            SET is_main = TRUE 
            WHERE id = (
                SELECT id FROM photos 
                WHERE user_id = user_record.user_id 
                ORDER BY created_at ASC 
                LIMIT 1
            );
        -- If multiple main photos, keep only the first one
        ELSIF main_photo_count > 1 THEN
            -- First, unset all main photos for this user
            UPDATE photos SET is_main = FALSE WHERE user_id = user_record.user_id;
            
            -- Then set the first photo as main
            UPDATE photos 
            SET is_main = TRUE 
            WHERE id = (
                SELECT id FROM photos 
                WHERE user_id = user_record.user_id 
                ORDER BY created_at ASC 
                LIMIT 1
            );
        END IF;
    END LOOP;
END $$;

-- Show results
SELECT 'Photos schema updated successfully!' as message;

SELECT 'Photos table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'photos' 
ORDER BY ordinal_position;

SELECT 'Photo statistics:' as info;
SELECT 
    COUNT(*) as total_photos,
    COUNT(DISTINCT user_id) as users_with_photos,
    COUNT(CASE WHEN is_main THEN 1 END) as main_photos
FROM photos;

SELECT 'Sample photos by user:' as info;
SELECT 
    u.name,
    COUNT(p.id) as photo_count,
    COUNT(CASE WHEN p.is_main THEN 1 END) as main_photos
FROM users u
LEFT JOIN photos p ON u.id = p.user_id
GROUP BY u.id, u.name
ORDER BY u.id
LIMIT 10;
