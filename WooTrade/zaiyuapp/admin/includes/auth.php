<?php
/**
 * Authentication System
 * Admin Panel for ZaiYu Dating App
 */

session_start();
require_once '../config/database.php';

class Auth {
    
    public static function login($username, $password) {
        if ($username === AdminConfig::ADMIN_USERNAME && $password === AdminConfig::ADMIN_PASSWORD) {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;
            $_SESSION['admin_login_time'] = time();
            return true;
        }
        return false;
    }
    
    public static function logout() {
        session_destroy();
        redirectTo('login.php');
    }
    
    public static function isLoggedIn() {
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['admin_login_time'])) {
            if (time() - $_SESSION['admin_login_time'] > AdminConfig::SESSION_TIMEOUT) {
                self::logout();
                return false;
            }
        }
        
        return true;
    }
    
    public static function requireLogin() {
        if (!self::isLoggedIn()) {
            redirectTo('login.php');
        }
    }
    
    public static function getUsername() {
        return isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : '';
    }
    
    public static function getLoginTime() {
        return isset($_SESSION['admin_login_time']) ? $_SESSION['admin_login_time'] : 0;
    }
}

// Auto-redirect if not logged in (except for login and logout pages)
$current_page = basename($_SERVER['PHP_SELF']);
if (!in_array($current_page, ['login.php', 'logout.php']) && !Auth::isLoggedIn()) {
    redirectTo('login.php');
}
?>
