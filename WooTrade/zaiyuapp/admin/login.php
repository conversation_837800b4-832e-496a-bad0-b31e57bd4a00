<?php
/**
 * Admin Login Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'config/database.php';
require_once 'includes/auth.php';

$error_message = '';
$success_message = '';

// Redirect if already logged in
if (Auth::isLoggedIn()) {
    redirectTo('dashboard.php');
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
    } else {
        if (Auth::login($username, $password)) {
            redirectTo('dashboard.php');
        } else {
            $error_message = 'Invalid username or password.';
        }
    }
}

$page_title = 'Login';
include 'includes/header.php';
?>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #6f42c1, #8b5cf6);">
            <div class="text-center text-white">
                <i class="fas fa-heart fa-5x mb-4"></i>
                <h1 class="display-4 fw-bold mb-3">ZaiYu Admin</h1>
                <p class="lead">Manage your dating platform with ease</p>
                <div class="mt-5">
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <p>User Management</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <p>Analytics</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-cog fa-2x mb-2"></i>
                            <p>Settings</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                            <h3 class="fw-bold">Admin Login</h3>
                            <p class="text-muted">Sign in to access the admin panel</p>
                        </div>
                        
                        <?php if ($error_message): ?>
                            <?php echo showAlert($error_message, 'danger'); ?>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <?php echo showAlert($success_message, 'success'); ?>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                    <div class="invalid-feedback">
                                        Please enter your username.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="invalid-feedback">
                                        Please enter your password.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> Sign In
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                Default credentials: admin / admin123<br>
                                <strong>Change these in production!</strong>
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        © <?php echo date('Y'); ?> ZaiYu Dating App. All rights reserved.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // Focus on username field
    document.getElementById('username').focus();
});
</script>

<?php include 'includes/footer.php'; ?>
