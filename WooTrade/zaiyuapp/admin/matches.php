<?php
/**
 * Matches Management Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'Matches';
$database = new Database();
$db = $database->getConnection();

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = AdminConfig::ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

try {
    // Get total count
    $stmt = $db->query("SELECT COUNT(*) as total FROM matches");
    $total_matches = $stmt->fetch()['total'];
    $total_pages = ceil($total_matches / $limit);
    
    // Get matches with user details
    $query = "
        SELECT m.id, m.created_at,
               u1.name as user1_name, u1.phone as user1_phone, u1.gender as user1_gender,
               u2.name as user2_name, u2.phone as user2_phone, u2.gender as user2_gender,
               (SELECT COUNT(*) FROM messages WHERE match_id = m.id) as message_count
        FROM matches m
        JOIN users u1 ON m.user1_id = u1.id
        JOIN users u2 ON m.user2_id = u2.id
        ORDER BY m.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $db->query($query);
    $matches = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error fetching matches: " . $e->getMessage();
    $matches = [];
    $total_matches = 0;
    $total_pages = 0;
}

include 'includes/header.php';
?>

<!-- Matches Content -->
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-heart"></i> Matches Management
        </h1>
    </div>
</div>

<?php if (isset($error_message)): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Matches Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">All Matches (<?php echo number_format($total_matches); ?> total)</h5>
                <button class="btn btn-sm btn-outline-light" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Refresh
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Match ID</th>
                                <th>User 1</th>
                                <th>User 2</th>
                                <th>Messages</th>
                                <th>Matched On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($matches)): ?>
                                <?php foreach ($matches as $match): ?>
                                    <tr>
                                        <td><?php echo $match['id']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php echo getGenderIcon($match['user1_gender']); ?>
                                                <div class="ms-2">
                                                    <strong><?php echo htmlspecialchars($match['user1_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($match['user1_phone']); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php echo getGenderIcon($match['user2_gender']); ?>
                                                <div class="ms-2">
                                                    <strong><?php echo htmlspecialchars($match['user2_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($match['user2_phone']); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $match['message_count']; ?> messages</span>
                                        </td>
                                        <td>
                                            <small><?php echo formatDate($match['created_at']); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" 
                                                        onclick="viewMessages(<?php echo $match['id']; ?>)"
                                                        data-bs-toggle="tooltip" title="View Messages">
                                                    <i class="fas fa-comments"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-delete"
                                                        onclick="deleteMatch(<?php echo $match['id']; ?>)"
                                                        data-bs-toggle="tooltip" title="Delete Match">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-heart fa-3x mb-3 d-block"></i>
                                        No matches found
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Matches pagination">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>

<script>
function viewMessages(matchId) {
    alert('View messages functionality would be implemented here for match ID: ' + matchId);
}

function deleteMatch(matchId) {
    if (confirm('Are you sure you want to delete this match? This action cannot be undone.')) {
        // Implementation would go here
        alert('Delete match functionality would be implemented here for match ID: ' + matchId);
    }
}
</script>

<?php include 'includes/footer.php'; ?>
