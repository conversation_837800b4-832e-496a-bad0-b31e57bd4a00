<?php
/**
 * Photo Actions Handler
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
if (!Auth::isLoggedIn()) {
    redirectTo('login.php');
}

$database = new Database();
$db = $database->getConnection();

$action = $_POST['action'] ?? '';
$photo_id = (int)($_POST['photo_id'] ?? 0);
$user_id = (int)($_POST['user_id'] ?? 0);

if (!$action || !$photo_id || !$user_id) {
    redirectTo('users.php');
}

try {
    switch ($action) {
        case 'set_main':
            // First, unset all main photos for this user
            $stmt = $db->prepare("UPDATE photos SET is_main = FALSE WHERE user_id = ?");
            $stmt->execute([$user_id]);
            
            // Then set the selected photo as main
            $stmt = $db->prepare("UPDATE photos SET is_main = TRUE WHERE id = ? AND user_id = ?");
            $stmt->execute([$photo_id, $user_id]);
            
            $success_message = "Main photo updated successfully!";
            break;
            
        case 'delete':
            // Check if this is the main photo
            $stmt = $db->prepare("SELECT is_main FROM photos WHERE id = ? AND user_id = ?");
            $stmt->execute([$photo_id, $user_id]);
            $photo = $stmt->fetch();
            
            if ($photo) {
                // Delete the photo
                $stmt = $db->prepare("DELETE FROM photos WHERE id = ? AND user_id = ?");
                $stmt->execute([$photo_id, $user_id]);
                
                // If it was the main photo, set another photo as main (if any exist)
                if ($photo['is_main']) {
                    $stmt = $db->prepare("
                        UPDATE photos 
                        SET is_main = TRUE 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    ");
                    $stmt->execute([$user_id]);
                }
                
                $success_message = "Photo deleted successfully!";
            } else {
                $error_message = "Photo not found!";
            }
            break;
            
        default:
            $error_message = "Invalid action!";
            break;
    }
    
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
}

// Redirect back to edit user page with message
$redirect_url = "edit_user.php?id=" . $user_id;
if (isset($success_message)) {
    $redirect_url .= "&success=" . urlencode($success_message);
} elseif (isset($error_message)) {
    $redirect_url .= "&error=" . urlencode($error_message);
}

redirectTo($redirect_url);
?>
