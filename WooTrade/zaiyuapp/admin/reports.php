<?php
/**
 * Reports Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'Reports';
$database = new Database();
$db = $database->getConnection();

// Get date range from query parameters
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

try {
    // User registration stats
    $stmt = $db->prepare("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM users 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $stmt->execute([$start_date, $end_date]);
    $registration_stats = $stmt->fetchAll();
    
    // Match stats
    $stmt = $db->prepare("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM matches 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $stmt->execute([$start_date, $end_date]);
    $match_stats = $stmt->fetchAll();
    
    // Activity stats
    $stmt = $db->prepare("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM activities 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $stmt->execute([$start_date, $end_date]);
    $activity_stats = $stmt->fetchAll();
    
    // Top locations
    $stmt = $db->query("
        SELECT current_location, COUNT(*) as user_count 
        FROM users 
        WHERE current_location IS NOT NULL 
        GROUP BY current_location 
        ORDER BY user_count DESC 
        LIMIT 10
    ");
    $location_stats = $stmt->fetchAll();
    
    // Age distribution
    $stmt = $db->query("
        SELECT 
            CASE 
                WHEN EXTRACT(YEAR FROM AGE(birthday)) < 25 THEN 'Under 25'
                WHEN EXTRACT(YEAR FROM AGE(birthday)) BETWEEN 25 AND 30 THEN '25-30'
                WHEN EXTRACT(YEAR FROM AGE(birthday)) BETWEEN 31 AND 35 THEN '31-35'
                WHEN EXTRACT(YEAR FROM AGE(birthday)) BETWEEN 36 AND 40 THEN '36-40'
                WHEN EXTRACT(YEAR FROM AGE(birthday)) > 40 THEN 'Over 40'
                ELSE 'Unknown'
            END as age_group,
            COUNT(*) as count
        FROM users 
        WHERE birthday IS NOT NULL
        GROUP BY age_group 
        ORDER BY count DESC
    ");
    $age_stats = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error fetching reports: " . $e->getMessage();
}

include 'includes/header.php';
?>

<!-- Reports Content -->
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-bar"></i> Reports & Analytics
        </h1>
    </div>
</div>

<?php if (isset($error_message)): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="<?php echo htmlspecialchars($start_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="<?php echo htmlspecialchars($end_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Apply Filter
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportReport()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- User Registration Trend -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus"></i> User Registration Trend</h5>
            </div>
            <div class="card-body">
                <canvas id="registrationChart" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Match Creation Trend -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-heart"></i> Match Creation Trend</h5>
            </div>
            <div class="card-body">
                <canvas id="matchChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Demographics Row -->
<div class="row mb-4">
    <!-- Age Distribution -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-birthday-cake"></i> Age Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="ageChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Top Locations -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Top Locations</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Location</th>
                                <th>Users</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($location_stats)): ?>
                                <?php 
                                $total_location_users = array_sum(array_column($location_stats, 'user_count'));
                                foreach ($location_stats as $location): 
                                    $percentage = $total_location_users > 0 ? 
                                        round(($location['user_count'] / $total_location_users) * 100, 1) : 0;
                                ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($location['current_location']); ?></td>
                                        <td><?php echo number_format($location['user_count']); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                    <div class="progress-bar" style="width: <?php echo $percentage; ?>%"></div>
                                                </div>
                                                <small><?php echo $percentage; ?>%</small>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center text-muted">No location data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activity Statistics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Activity Statistics</h5>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
// User Registration Chart
const registrationData = <?php echo json_encode($registration_stats); ?>;
const registrationLabels = registrationData.map(item => item.date);
const registrationCounts = registrationData.map(item => parseInt(item.count));

const registrationCtx = document.getElementById('registrationChart').getContext('2d');
new Chart(registrationCtx, {
    type: 'line',
    data: {
        labels: registrationLabels,
        datasets: [{
            label: 'New Users',
            data: registrationCounts,
            borderColor: '#6f42c1',
            backgroundColor: 'rgba(111, 66, 193, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: { stepSize: 1 }
            }
        },
        plugins: {
            legend: { display: false }
        }
    }
});

// Match Creation Chart
const matchData = <?php echo json_encode($match_stats); ?>;
const matchLabels = matchData.map(item => item.date);
const matchCounts = matchData.map(item => parseInt(item.count));

const matchCtx = document.getElementById('matchChart').getContext('2d');
new Chart(matchCtx, {
    type: 'bar',
    data: {
        labels: matchLabels,
        datasets: [{
            label: 'New Matches',
            data: matchCounts,
            backgroundColor: '#dc3545',
            borderColor: '#dc3545',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: { stepSize: 1 }
            }
        },
        plugins: {
            legend: { display: false }
        }
    }
});

// Age Distribution Chart
const ageData = <?php echo json_encode($age_stats); ?>;
const ageLabels = ageData.map(item => item.age_group);
const ageCounts = ageData.map(item => parseInt(item.count));

const ageCtx = document.getElementById('ageChart').getContext('2d');
new Chart(ageCtx, {
    type: 'doughnut',
    data: {
        labels: ageLabels,
        datasets: [{
            data: ageCounts,
            backgroundColor: [
                '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#17a2b8'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
            legend: { position: 'bottom' }
        }
    }
});

// Activity Chart
const activityData = <?php echo json_encode($activity_stats); ?>;
const activityLabels = activityData.map(item => item.date);
const activityCounts = activityData.map(item => parseInt(item.count));

const activityCtx = document.getElementById('activityChart').getContext('2d');
new Chart(activityCtx, {
    type: 'line',
    data: {
        labels: activityLabels,
        datasets: [{
            label: 'New Activities',
            data: activityCounts,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: { stepSize: 1 }
            }
        },
        plugins: {
            legend: { display: false }
        }
    }
});

function exportReport() {
    alert('Export functionality would be implemented here');
}
</script>

<?php include 'includes/footer.php'; ?>
