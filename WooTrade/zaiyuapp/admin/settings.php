<?php
/**
 * Settings Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'Settings';
$success_message = '';
$error_message = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'change_password':
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if ($current_password !== AdminConfig::ADMIN_PASSWORD) {
                $error_message = "Current password is incorrect.";
            } elseif ($new_password !== $confirm_password) {
                $error_message = "New passwords do not match.";
            } elseif (strlen($new_password) < 6) {
                $error_message = "New password must be at least 6 characters long.";
            } else {
                // In a real application, you would update the password in the database
                $success_message = "Password change functionality would be implemented here. In production, store hashed passwords in database.";
            }
            break;
            
        case 'update_settings':
            // Handle general settings update
            $success_message = "Settings updated successfully.";
            break;
    }
}

include 'includes/header.php';
?>

<!-- Settings Content -->
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-cog"></i> Settings
        </h1>
    </div>
</div>

<?php if ($success_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($success_message, 'success'); ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Account Settings -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-cog"></i> Account Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <div class="invalid-feedback">
                            Please enter your current password.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                        <div class="invalid-feedback">
                            Password must be at least 6 characters long.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               minlength="6" required>
                        <div class="invalid-feedback">
                            Please confirm your new password.
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Change Password
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Application Settings -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sliders-h"></i> Application Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="mb-3">
                        <label for="items_per_page" class="form-label">Items Per Page</label>
                        <select class="form-select" id="items_per_page" name="items_per_page">
                            <option value="10" <?php echo AdminConfig::ITEMS_PER_PAGE == 10 ? 'selected' : ''; ?>>10</option>
                            <option value="20" <?php echo AdminConfig::ITEMS_PER_PAGE == 20 ? 'selected' : ''; ?>>20</option>
                            <option value="50" <?php echo AdminConfig::ITEMS_PER_PAGE == 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo AdminConfig::ITEMS_PER_PAGE == 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                               value="<?php echo AdminConfig::SESSION_TIMEOUT / 60; ?>" min="5" max="480">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode">
                            <label class="form-check-label" for="maintenance_mode">
                                Enable Maintenance Mode
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Application Version:</strong></td>
                                <td><?php echo AdminConfig::APP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td><strong>PHP Version:</strong></td>
                                <td><?php echo phpversion(); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Server Software:</strong></td>
                                <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Database:</strong></td>
                                <td>PostgreSQL</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Current User:</strong></td>
                                <td><?php echo Auth::getUsername(); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Login Time:</strong></td>
                                <td><?php echo date('Y-m-d H:i:s', Auth::getLoginTime()); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Server Time:</strong></td>
                                <td><?php echo date('Y-m-d H:i:s'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Timezone:</strong></td>
                                <td><?php echo date_default_timezone_get(); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Database Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-database"></i> Database Status</h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    if ($db) {
                        echo '<div class="alert alert-success"><i class="fas fa-check-circle"></i> Database connection is working properly.</div>';
                        
                        // Get database size
                        $stmt = $db->query("SELECT pg_size_pretty(pg_database_size(current_database())) as size");
                        $db_size = $stmt->fetch()['size'];
                        
                        echo '<p><strong>Database Size:</strong> ' . htmlspecialchars($db_size) . '</p>';
                        
                        // Get table counts
                        $tables = ['users', 'matches', 'messages', 'activities', 'photos'];
                        echo '<div class="row">';
                        foreach ($tables as $table) {
                            try {
                                $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                                $count = $stmt->fetch()['count'];
                                echo '<div class="col-md-2 text-center">';
                                echo '<h4>' . number_format($count) . '</h4>';
                                echo '<p class="mb-0">' . ucfirst($table) . '</p>';
                                echo '</div>';
                            } catch (Exception $e) {
                                echo '<div class="col-md-2 text-center">';
                                echo '<h4>-</h4>';
                                echo '<p class="mb-0">' . ucfirst($table) . '</p>';
                                echo '</div>';
                            }
                        }
                        echo '</div>';
                    } else {
                        echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Unable to connect to database.</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity("Passwords don't match");
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
});
</script>

<?php include 'includes/footer.php'; ?>
