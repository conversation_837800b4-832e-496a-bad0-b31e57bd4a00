#!/bin/bash

# ZaiYu Admin Panel Setup Script
# This script helps set up the admin panel on Apache server

echo "🚀 ZaiYu Admin Panel Setup"
echo "=========================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Variables
ADMIN_DIR="/var/www/html/zaiyuapp-admin"
CURRENT_DIR=$(pwd)

echo "📁 Setting up admin panel directory..."

# Create admin directory if it doesn't exist
if [ ! -d "$ADMIN_DIR" ]; then
    mkdir -p "$ADMIN_DIR"
    echo "✅ Created directory: $ADMIN_DIR"
else
    echo "ℹ️  Directory already exists: $ADMIN_DIR"
fi

# Copy files
echo "📋 Copying admin panel files..."
cp -r "$CURRENT_DIR"/* "$ADMIN_DIR/"
echo "✅ Files copied successfully"

# Set permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data "$ADMIN_DIR"
chmod -R 755 "$ADMIN_DIR"
echo "✅ Permissions set"

# Create Apache virtual host configuration
echo "🌐 Creating Apache virtual host..."
cat > /etc/apache2/sites-available/zaiyuapp-admin.conf << EOF
<VirtualHost *:80>
    ServerName admin.zaiyuapp.local
    DocumentRoot $ADMIN_DIR
    
    <Directory $ADMIN_DIR>
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/zaiyuapp-admin_error.log
    CustomLog \${APACHE_LOG_DIR}/zaiyuapp-admin_access.log combined
</VirtualHost>
EOF

# Enable the site
a2ensite zaiyuapp-admin.conf
echo "✅ Virtual host created and enabled"

# Enable required Apache modules
echo "🔧 Enabling Apache modules..."
a2enmod rewrite
a2enmod php8.1  # Adjust PHP version as needed

# Restart Apache
echo "🔄 Restarting Apache..."
systemctl restart apache2

if [ $? -eq 0 ]; then
    echo "✅ Apache restarted successfully"
else
    echo "❌ Failed to restart Apache. Please check the configuration."
    exit 1
fi

# Add to hosts file for local development
echo "🏠 Adding to hosts file..."
if ! grep -q "admin.zaiyuapp.local" /etc/hosts; then
    echo "127.0.0.1    admin.zaiyuapp.local" >> /etc/hosts
    echo "✅ Added admin.zaiyuapp.local to hosts file"
else
    echo "ℹ️  admin.zaiyuapp.local already in hosts file"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📍 Admin panel is now available at:"
echo "   • http://localhost/zaiyuapp-admin"
echo "   • http://admin.zaiyuapp.local"
echo ""
echo "🔑 Default login credentials:"
echo "   • Username: admin"
echo "   • Password: admin123"
echo ""
echo "⚠️  IMPORTANT SECURITY NOTES:"
echo "   • Change the default admin password immediately!"
echo "   • Update database credentials in config/database.php"
echo "   • Use HTTPS in production"
echo "   • Consider IP whitelisting for admin access"
echo ""
echo "📚 For more information, see README.md"
echo ""

# Check if PostgreSQL is running
if systemctl is-active --quiet postgresql; then
    echo "✅ PostgreSQL is running"
else
    echo "⚠️  PostgreSQL is not running. Please start it:"
    echo "   sudo systemctl start postgresql"
fi

# Check PHP extensions
echo "🔍 Checking PHP extensions..."
php -m | grep -q pdo_pgsql
if [ $? -eq 0 ]; then
    echo "✅ PDO PostgreSQL extension is installed"
else
    echo "❌ PDO PostgreSQL extension is missing. Install it:"
    echo "   sudo apt-get install php-pgsql"
fi

echo ""
echo "🚀 You can now access the admin panel!"
