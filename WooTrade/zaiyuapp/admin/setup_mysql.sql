-- MySQL Database Setup for ZaiYu Admin Panel
-- Run this script to create the database and tables

-- Create database
CREATE DATABASE IF NOT EXISTS secondchance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE secondchance_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    gender ENUM('男', '女') NOT NULL,
    birthday DATE,
    current_location VARCHAR(200),
    status ENUM('active', 'blocked', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_phone (phone),
    INDEX idx_gender (gender),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Matches table
CREATE TABLE IF NOT EXISTS matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user1_id INT NOT NULL,
    user2_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user1_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (user2_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_match (user1_id, user2_id),
    INDEX idx_created_at (created_at)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    match_id INT NOT NULL,
    sender_id INT NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_match_id (match_id),
    INDEX idx_created_at (created_at)
);

-- Activities table
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    location VARCHAR(200),
    start_time DATETIME NOT NULL,
    max_participants INT DEFAULT 10,
    creator_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_start_time (start_time),
    INDEX idx_category (category),
    INDEX idx_created_at (created_at)
);

-- Activity participants table
CREATE TABLE IF NOT EXISTS activity_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('joined', 'left', 'pending') DEFAULT 'joined',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_participation (activity_id, user_id),
    INDEX idx_status (status),
    INDEX idx_joined_at (joined_at)
);

-- Photos table
CREATE TABLE IF NOT EXISTS photos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_main (is_main)
);

-- Insert some sample data for testing
INSERT IGNORE INTO users (name, phone, gender, birthday, current_location, status) VALUES
('张三', '13800138001', '男', '1990-05-15', '深圳市', 'active'),
('李四', '13800138002', '女', '1992-08-20', '深圳市', 'active'),
('王五', '13800138003', '男', '1988-12-10', '深圳市', 'active'),
('赵六', '13800138004', '女', '1995-03-25', '深圳市', 'blocked'),
('钱七', '13800138005', '男', '1987-11-30', '深圳市', 'active');

-- Insert some sample matches
INSERT IGNORE INTO matches (user1_id, user2_id) VALUES
(1, 2),
(1, 4),
(3, 2);

-- Insert some sample messages
INSERT IGNORE INTO messages (match_id, sender_id, content) VALUES
(1, 1, '你好！'),
(1, 2, '你好，很高兴认识你！'),
(1, 1, '我也是，你在深圳哪个区？'),
(2, 1, '嗨！'),
(3, 3, '你好美女！');

-- Insert some sample activities
INSERT IGNORE INTO activities (title, description, category, location, start_time, max_participants, creator_id) VALUES
('周末爬山', '一起去梧桐山爬山，锻炼身体，认识朋友', '户外运动', '梧桐山', '2024-01-20 09:00:00', 10, 1),
('咖啡聚会', '在星巴克喝咖啡聊天', '社交聚会', '海岸城星巴克', '2024-01-21 14:00:00', 6, 2),
('电影之夜', '一起看最新上映的电影', '娱乐', '万象城影院', '2024-01-22 19:00:00', 8, 3);

-- Insert some sample activity participants
INSERT IGNORE INTO activity_participants (activity_id, user_id, status) VALUES
(1, 1, 'joined'),
(1, 2, 'joined'),
(1, 3, 'joined'),
(2, 2, 'joined'),
(2, 4, 'joined'),
(3, 3, 'joined'),
(3, 1, 'joined');

-- Show success message
SELECT 'Database setup completed successfully!' as message;
SELECT 'Sample data inserted for testing' as message;
SELECT CONCAT('Total users: ', COUNT(*)) as message FROM users;
SELECT CONCAT('Total matches: ', COUNT(*)) as message FROM matches;
SELECT CONCAT('Total activities: ', COUNT(*)) as message FROM activities;
