<?php
/**
 * User Management Page
 * Admin Panel for ZaiYu Dating App
 */

require_once 'includes/auth.php';

$page_title = 'User Management';
$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'block':
                $stmt = $db->prepare("UPDATE users SET status = 'blocked' WHERE id = ?");
                $stmt->execute([$user_id]);
                $success_message = "User blocked successfully.";
                break;
                
            case 'unblock':
                $stmt = $db->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                $stmt->execute([$user_id]);
                $success_message = "User unblocked successfully.";
                break;
                
            case 'delete':
                // Soft delete - just mark as deleted
                $stmt = $db->prepare("UPDATE users SET deleted_at = NOW() WHERE id = ?");
                $stmt->execute([$user_id]);
                $success_message = "User deleted successfully.";
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Pagination and filtering
$page = (int)($_GET['page'] ?? 1);
$limit = AdminConfig::ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

$search = sanitizeInput($_GET['search'] ?? '');
$gender_filter = sanitizeInput($_GET['gender'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');

// Build query
$where_conditions = ["deleted_at IS NULL"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name ILIKE ? OR phone ILIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($gender_filter)) {
    $where_conditions[] = "gender = ?";
    $params[] = $gender_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "COALESCE(status, 'active') = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM users WHERE $where_clause";
    $stmt = $db->prepare($count_query);
    $stmt->execute($params);
    $total_users = $stmt->fetch()['total'];
    $total_pages = ceil($total_users / $limit);
    
    // Get users
    $query = "
        SELECT id, name, phone, gender, birthday, current_location, 
               COALESCE(status, 'active') as status, created_at, updated_at,
               (SELECT COUNT(*) FROM photos WHERE user_id = users.id) as photo_count
        FROM users 
        WHERE $where_clause 
        ORDER BY created_at DESC 
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error fetching users: " . $e->getMessage();
    $users = [];
    $total_users = 0;
    $total_pages = 0;
}

include 'includes/header.php';
?>

<!-- User Management Content -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users"></i> User Management
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i> Add User
            </button>
        </div>
    </div>
</div>

<?php if ($success_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($success_message, 'success'); ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="row">
        <div class="col-12">
            <?php echo showAlert($error_message, 'danger'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="Name or phone..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="gender" class="form-label">Gender</label>
                        <select class="form-select" id="gender" name="gender">
                            <option value="">All Genders</option>
                            <option value="男" <?php echo $gender_filter === '男' ? 'selected' : ''; ?>>男</option>
                            <option value="女" <?php echo $gender_filter === '女' ? 'selected' : ''; ?>>女</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="blocked" <?php echo $status_filter === 'blocked' ? 'selected' : ''; ?>>Blocked</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Users (<?php echo number_format($total_users); ?> total)</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-light" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Gender</th>
                                <th>Age</th>
                                <th>Location</th>
                                <th>Photos</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($users)): ?>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                        <td><?php echo getGenderIcon($user['gender']) . ' ' . $user['gender']; ?></td>
                                        <td>
                                            <?php 
                                            if ($user['birthday']) {
                                                echo formatAge($user['birthday']) . ' years';
                                            } else {
                                                echo '<span class="text-muted">N/A</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['current_location'] ?? 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $user['photo_count']; ?> photos</span>
                                        </td>
                                        <td><?php echo getStatusBadge($user['status']); ?></td>
                                        <td>
                                            <small><?php echo formatDate($user['created_at']); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" 
                                                        onclick="editUser(<?php echo $user['id']; ?>)"
                                                        data-bs-toggle="tooltip" title="Edit User">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <?php if ($user['status'] === 'blocked'): ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="unblock">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-success btn-unblock"
                                                                data-bs-toggle="tooltip" title="Unblock User">
                                                            <i class="fas fa-unlock"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="block">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-warning btn-block"
                                                                data-bs-toggle="tooltip" title="Block User">
                                                            <i class="fas fa-lock"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-danger btn-delete"
                                                            data-bs-toggle="tooltip" title="Delete User">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                        No users found
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="User pagination">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&gender=<?php echo urlencode($gender_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&gender=<?php echo urlencode($gender_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&gender=<?php echo urlencode($gender_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>

<script>
function editUser(userId) {
    // This would open an edit modal or redirect to edit page
    // For now, just show an alert
    alert('Edit user functionality would be implemented here for user ID: ' + userId);
}
</script>

<?php include 'includes/footer.php'; ?>
